{"name": "promptyoself-v3", "version": "1.0.0", "description": "PromptYoSelf V3 - AI-powered reminder and task management system", "dependencies": {"@modelcontextprotocol/server-sequential-thinking": "latest", "task-master-ai": "^0.15.0"}, "devDependencies": {"@playwright/test": "^1.52.0", "@smithery/cli": "latest", "@types/node": "^22.15.30"}, "scripts": {"install-global": "npm install -g task-master-ai @modelcontextprotocol/server-sequential-thinking @smithery/cli", "verify-tools": "npx --version && npm --version", "test-mcp": "npx -y @smithery/cli@latest run @xinzhongyouhai/mcp-sequentialthinking-tools --help"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}