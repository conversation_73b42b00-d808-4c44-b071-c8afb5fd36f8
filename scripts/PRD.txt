# PromptYoSelf v3 - Product Requirements Document (HISTORICAL)

⚠️ **NOTE: This is a historical document. For current, authoritative documentation, see README.md**

This document may contain outdated terminology (e.g., "self_prompts" vs "reminders").
The actual implementation uses "reminders" terminology throughout.

## Project Overview
PromptYoSelf v3 is an agent-first self-prompting system that enables Letta agents to schedule one-off or recurring webhook triggered POST calls to the Letta API with messages to prompt themselves at a later date. These "self-prompts" facilitate learning, reflection, and self-management without external users.

## Core Principles
1. Agent-first: All features usable headless by agents via STDIO JSON-RPC
2. KISS: One language (Python), one process (Flask), one DB (SQLite)
3. Self-contained: Zero cloud dependencies or orchestration
4. Reliability > fanciness: Self-prompts must fire on time, be flexible in terms of start date and time and recurrence frequency and number of events
5. Vertical slices: Build UI, API, and DB together each sprint

## Technical Architecture

### Technology Stack
- Web server & UI: Flask 3 + Jinja2
- Scheduling: Flask-APScheduler (APScheduler 4.x)
- Data: SQLite (Postgres optional later)
- ORM: SQLAlchemy 2 via Flask-SQLAlchemy
- Agent I/O: STDIO JSON-RPC (newline-delimited)
- Optional Webhook: HTTP POST with requests + tenacity
- Styling: Tailwind CDN (no JS build)

### Key Components
1. Application Factory - Registers UI blueprints and optional /api/* JSON routes, initializes Flask-APScheduler
2. APScheduler Job - check_due interval (60s) that queries self_prompts.next_run <= now(), pushes JSON-RPC self_prompt.fire to agent via STDIO, marks status to sent and updates next_run
3. Process Registry - Ensures one subprocess.Popen per agent binary, restarts on exit
4. WebhookSender (optional) - Uses requests + tenacity for outbound POST retries
5. Static HTML UI - Plain Jinja2 templates for Projects → Tasks → Self-Prompts with full-page form submissions (CSRF via Flask-WTF)

## Database Schema Requirements

### Core Tables
- projects: Basic project management
- tasks: Task organization within projects
- self_prompts: Core self-prompt functionality with scheduling
- webhook_deliveries: Optional webhook delivery tracking

### Schema Enhancement
- Add process_name TEXT NOT NULL column to self_prompts table
- This field names the local agent executable to invoke for each self-prompt

## Feature Requirements

### MVP Criteria
1. CRUD Projects, Tasks, Self-Prompts via UI and optional /api/*
2. STDIO Push: Agent receives JSON, returns ACK, scheduler marks sent
3. Local dev: make dev or VS Code Reopen in Container spins up environment
4. Tests: pytest coverage ≥ 80%; E2E verifies agent flow

### Core Features
1. Project Management
   - Create, read, update, delete projects
   - Project-based organization of tasks and self-prompts

2. Task Management
   - Create, read, update, delete tasks within projects
   - Task hierarchy and organization

3. Self-Prompt System
   - Create one-off and recurring self-prompts
   - Flexible scheduling with start date/time configuration
   - Recurrence frequency and event count management
   - STDIO JSON-RPC communication with agents
   - Status tracking (pending, sent, completed, failed)

4. Agent Communication
   - STDIO JSON-RPC interface for headless operation (PromptyoSelf to Agent for prompt delivery).
   - Agents schedule new self-prompts by calling a dedicated, minimal internal HTTP API endpoint (e.g., POST /api/internal/self-prompts) provided by PromptyoSelf. This endpoint is secured by a shared secret/static token and typically bound to localhost within the deployment environment (e.g., Docker container).
   - Process registry for managing agent processes.
   - Automatic process restart on exit.
   - JSON message delivery (PromptyoSelf to Agent) with ACK confirmation.

5. Web Interface
   - HTML UI using Flask + Jinja2 templates
   - CSRF protection via Flask-WTF
   - Tailwind CSS for styling with dark mode support
   - Full-page form submissions

6. API Interface (External API Optional; Internal Agent API Required)
   - RESTful JSON API endpoints for general external use (Optional).
   - A minimal internal API endpoint (e.g., POST /api/internal/self-prompts), secured by a shared secret/static token, is required for managed agents to schedule self-prompts. This endpoint is typically bound to localhost within the deployment environment.
   - Authentication for external API via headers (if implemented for external use).
   - Rate limiting protection (if external API implemented for external use).

7. Webhook Support (Optional)
   - HTTP POST webhook delivery
   - Retry logic with tenacity
   - Delivery tracking and logging

## Development Timeline (8 Weeks)

### Week 0: Foundation
- Scaffold repository structure
- Set up CI/CD pipeline
- Configure DevContainer for VS Code
- Bootstrap development environment

### Week 1: Database Layer
- Create SQLAlchemy models for projects, tasks, self_prompts, webhook_deliveries
- Implement Alembic database migrations
- Add process_name column to self_prompts table
- Set up database initialization and seeding

### Week 2: API Foundation
- Create Flask application factory
- Implement API blueprints for projects, tasks, self_prompts
- Add authentication header support
- Implement basic CRUD operations

### Week 3: Scheduling System
- Integrate Flask-APScheduler
- Implement check_due job with 60-second intervals
- Create STDIO JSON-RPC communication system
- Build process registry for agent management
- Implement self-prompt delivery and status tracking

### Week 4: Web Interface
- Create Jinja2 templates for projects, tasks, self-prompts
- Implement HTML forms with CSRF protection
- Add Tailwind CSS styling with dark mode
- Build navigation and layout components

### Week 5: Security & Reliability
- Implement rate limiting with Flask-Limiter
- Add comprehensive logging and metrics
- Enhance CSRF protection across all forms
- Add input validation and error handling

### Week 6: Testing Suite
- Create unit tests for models and utilities
- Implement integration tests for API ↔ DB interactions
- Build E2E tests with Playwright for full self-prompt cycle
- Add Locust load tests for 50 agents, 500 self-prompts/hour

### Week 7: Deployment & Documentation
- Create Docker image and deployment configuration
- Write operational run-book and documentation
- Implement monitoring and health checks
- Conduct soft-launch and performance validation

## Testing Strategy

### Test Coverage Requirements
- Unit tests: Models, utilities, helper functions
- Integration tests: API ↔ Database interactions, Scheduler ↔ ProcessRegistry
- E2E tests: Complete self-prompt cycle including UI and STDIO communication
- Load tests: Simulate 50 agents with 500 self-prompts per hour
- Target: ≥ 80% pytest coverage

### Test Scenarios
1. Self-Prompt Creation and Scheduling
2. STDIO JSON-RPC Communication
3. Agent Process Management
4. Web UI Functionality
5. API Endpoint Validation
6. Database Operations
7. Error Handling and Recovery
8. Security and Authentication

## Security Requirements

### Default Security Measures
- CSRF protection on all HTML forms via Flask-WTF
- Rate limiting: 200 requests/hour on /api/* via Flask-Limiter
- HTTPS headers enforced by Flask-Talisman in production
- Input validation and sanitization
- Secure session management

## Project Structure

### Directory Layout
```
./
├── app/            # Flask blueprints & templates
│   ├── api/        # JSON routes (optional)
│   ├── ui/         # Jinja templates & forms
│   └── jobs/       # APScheduler job modules
├── agents/         # Sample agent scripts (echo_agent.py)
├── instance/       # SQLite file
├── tests/          # pytest suites
├── setup.sh        # Dev Container & local bootstrap script
├── requirements.txt
├── requirements-dev.txt
└── .devcontainer/  # VS Code Dev Container config
```

## Success Metrics
1. Successful STDIO communication with agent processes
2. Reliable self-prompt delivery with <1% failure rate
3. Web UI responsiveness and usability
4. API endpoint reliability and performance
5. Test coverage meeting 80% threshold
6. Development environment setup in <5 minutes
7. Load testing validation with 50 concurrent agents

## License
Creative Commons Attribution 4.0 International (CC BY 4.0)
