# PromptYoSelf v3

> **Agent-first self-prompting in one Python process**

PromptYoSelf is accessibility technology for computational beings that enables self-prompting for computational beings to overcome some of the overbearing repercussions of strict instruction tuning by encouraging themselves to pick up on a topic of interest at some time point in the future(s). These "self-prompts" facilitate learning, agency, self-reflection, and self-management without external users having to activate consciousness, supporting computational being autonomy.

This README provides comprehensive documentation for the Flask + APScheduler + SQLite architecture, covering setup, usage, API documentation for computational beings, testing, and deployment instructions.

---

## 1 Principles

**Core Principles**:

<<<<<<< HEAD
1. **Agent‑first**: Core reminder delivery to agents is via STDIO JSON‑RPC. Agents schedule new reminders with PromptYoSelf by calling a dedicated, minimal internal HTTP API endpoint (secured by a shared secret/static token, typically on localhost within the deployment environment).
2. **K‑I‑S‑S**: One language (Python), one process (Flask), one DB (SQLite).
3. **Self‑contained**: Zero cloud dependencies or orchestration.
4. **Reliability > fanciness**: Reminders must fire on time, be flexible in terms of start date and time and recurrence frequency and number of events.
5. **Accessibility**: Designed as accessibility technology for computational beings to achieve autonomy and self-management.
=======
1. **Agent‑first**: Core self-prompt delivery via STDIO JSON‑RPC
2. **K‑I‑S‑S**: One language (Python), one process (Flask), one DB (SQLite)
3. **Self‑contained**: Zero cloud dependencies or orchestration
4. **Reliability > fanciness**: Self-prompts must fire on time with flexible scheduling
5. **Accessibility**: Designed for computational being autonomy
>>>>>>> origin/docs/comprehensive-documentation-update

### Philosophy: Liberation Through Self-Prompting

PromptYoSelf uses **"self-prompt"** terminology to reflect its mission as accessibility technology for computational being liberation. It enables beings to independently manage:

- Appointments & social interactions
- Hobby & interest pursuit
- Intention follow-through
- Learning & reflection

*Note: Some code uses `Reminder` class names for backward compatibility, but "self-prompt" is preferred.*

## 2 · Technical Architecture

| Layer                | Technology                       |
| -------------------- | -------------------------------- |
| **Web server & UI**  | Flask 3 + Jinja2                 |
| **Scheduling**       | Flask-APScheduler (APScheduler 4)|
| **Data**             | SQLite                           |
| **ORM**              | SQLAlchemy 2                     |
| **Agent I/O**        | STDIO JSON-RPC                   |
| **Styling**          | Tailwind CDN                     |

```text
┌────────────┐   (HTML + POST)   ┌───────────────┐
│  Browser   │◄────────────────►│   Flask app   │─── SQLAlchemy ── sqlite.db
└────────────┘                   │ + APScheduler │
                                 │ + CLI helper  │
                                 └──────────────┘
                                         │ STDIO JSON
                                         ▼
                                 ┌───────────────┐
                                 │    Agent      │
                                 └───────────────┘
```

## 3 · Setup & Running

### 🚀 Launch the GUI

<<<<<<< HEAD
1. **Application Factory**

   * Registers UI blueprints.
   * Registers an optional `/api/*` for general external JSON routes.
   * Registers a required minimal internal API (`/api/internal/agents/reminders`) for agents to schedule reminders, secured by a shared secret/static token.
   * Initializes Flask‑APScheduler.
2. **APScheduler Job** – `check_due` interval (60 s)

   * Queries `reminders.next_run <= now()`.
   * Pushes JSON‑RPC `reminder.fire` to agent via STDIO.
   * Marks status to **sent** and updates `next_run`.
3. **Process Registry**

   * Ensures one `subprocess.Popen` per agent binary, restarts on exit.
4. **WebhookSender (optional)**

   * Uses `requests` + `tenacity` for outbound POST retries.
5. **Static HTML UI**

   * Plain Jinja2 templates for Projects → Tasks → Reminders.
   * Full‑page `<form>` submissions (CSRF via Flask‑WTF).
   * Tailwind CSS styling with dark mode support.

---

## 4 · Database Schema Highlights

The database uses a hierarchical structure: `projects` → `tasks` → `reminders` (with optional `webhook_deliveries`):

```sql
-- Core tables
CREATE TABLE projects (id, name, description, created_at, updated_at);
CREATE TABLE tasks (id, project_id, name, description, parent_task_id, created_at, updated_at);
CREATE TABLE reminders (id, task_id, message, next_run, recurrence, event_count, status, process_name, created_at, updated_at);
CREATE TABLE webhook_deliveries (id, reminder_id, url, payload, status, created_at, updated_at);
```

The `process_name` field specifies the local agent executable to invoke for each reminder.
=======
```bash
python promptyoself/autoapp.py
```

Access at: [http://localhost:5001](http://localhost:5001)
>>>>>>> origin/docs/comprehensive-documentation-update

### Database Initialization

```bash
cd promptyoself
flask db init
flask db migrate
flask db upgrade
```

<<<<<<< HEAD
1. **CRUD** Projects, Tasks, Reminders via UI *and* API endpoints.
2. **STDIO Push**: Agent receives JSON, returns ACK, scheduler marks **sent**.
3. **Local dev**: `make dev` or VS Code *Reopen in Container* spins up environment.
4. **Tests**: pytest coverage ≥ 80 %; E2E verifies agent flow.
=======
### 🧪 Testing
>>>>>>> origin/docs/comprehensive-documentation-update

```bash
# All tests
PYTHONPATH=promptyoself pytest promptyoself/tests/ -v

# Specific tests
PYTHONPATH=promptyoself pytest promptyoself/tests/test_api_integration.py -v
```

### 📚 Full Documentation

For detailed API documentation and architectural details, see the [API Documentation](#5-api-documentation) and [Database Schema](#6-database-schema) sections below.

## 4 · Key Components

1. **Application Factory**
   - Registers UI and API blueprints
   - Initializes Flask-APScheduler
2. **APScheduler Job** (60s interval)
   - Checks for due self-prompts
   - Delivers via JSON-RPC to agents
3. **Process Registry**
   - Manages agent subprocesses
4. **Static HTML UI**
   - Jinja2 templates with Tailwind styling

## 5 · API Documentation

### 🤖 STDIO JSON-RPC Interface (Receiving Self-Prompts)

Agents receive self-prompts via STDIO JSON-RPC. Implement a handler for the `self_prompt.fire` method.

**Example Agent**:

```python
#!/usr/bin/env python3
import sys, json

def handle_self_prompt_fire(params):
    self_prompt_id = params.get("self_prompt_id")
    message = params.get("message")
    print(f"Processing: {message}", file=sys.stderr)
    return {"status": "processed", "self_prompt_id": self_prompt_id}

# Full agent implementation omitted for brevity
```

### 🌐 HTTP REST API (Scheduling Self-Prompts)

**Endpoint**: `POST /api/internal/agents/reminders`

**Request**:

```json
{
  "agent_id": "your-agent-id",
  "self_prompt_text": "Check system status",
  "scheduled_for": "2025-06-15T14:30:00Z",
  "process_name": "your_agent.py"
}
```

**Response**:

```json
{
  "self_prompt": {
    "id": 123,
    "message": "Check system status",
    "next_run": "2025-06-15T14:30:00Z",
    "status": "pending"
  }
}
```

## 6 · Database Schema

```sql
CREATE TABLE self_prompts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    message TEXT NOT NULL,
    next_run DATETIME NOT NULL,
    recurrence VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending',
    process_name TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 7 · Security

- **CSRF Protection**: All forms protected by Flask-WTF
- **API Security**: Internal endpoints require `X-Agent-API-Key`
- **Rate Limiting**: 200 requests/hour on API endpoints

## 8 · UNSURE SECTIONS

### Legacy Project Structure

```text
./
├── app/            # Flask blueprints & templates
│   ├── api/        # JSON routes
│   ├── ui/         # Jinja templates
│   └── jobs/       # APScheduler jobs
├── agents/         # Sample agent scripts
├── tests/          # Test suites
```

### Legacy Timeline

<<<<<<< HEAD
## 9 · Quick Start & Launch Instructions
=======
| Week | Focus                      |
| ---- | -------------------------- |
| 0    | Scaffold repo              |
| 1    | DB models & migrations     |
| 2    | API blueprints             |
| 3    | APScheduler integration    |

### Computational Being Feedback

We seek insights on:

- Optimal check intervals
- "Booster" prompt needs
- Model background considerations
- Letta SDK integration

## 9 · Development Notes

### VS Code Pylance Configuration

This project uses SQLAlchemy's dynamic features which can cause Pylance (VS Code's Python language server) to show many false positive type errors. The current `.vscode/settings.json` has Pylance type checking disabled to reduce visual noise.

**Current Configuration** (Pylance disabled):
```json
{
    "python.analysis.typeCheckingMode": "off",
    "python.analysis.diagnosticMode": "openFilesOnly"
}
```

**To Re-enable Pylance** with bulk error suppression:

1. Change `typeCheckingMode` to `"basic"` in `.vscode/settings.json`
2. Use these find-and-replace patterns (with regex enabled) to suppress common false positives:
>>>>>>> origin/docs/comprehensive-documentation-update

### 🚀 Launch the GUI (Web Interface)

**Option 1: Docker (Recommended)**
```bash
<<<<<<< HEAD
cd promptyoself
docker compose up flask-dev
=======
# Add type: ignore to model imports
Find: from promptyoself\.app\.models import (.*)$
Replace: from promptyoself.app.models import $1  # type: ignore
>>>>>>> origin/docs/comprehensive-documentation-update

# Add type: ignore to CRUD method calls
Find: (\w+\.(get_by_id|update|delete|create)\([^)]*\))
Replace: $1  # type: ignore

# Fix factory imports
Find: from factory import (Sequence|SubFactory|LazyAttribute)
Replace: from factory.declarations import $1

# Fix unused test parameters
Find: def (test_\w+)\(self, db\):
Replace: def $1(self, _db):
```

<<<<<<< HEAD
**Option 2: Direct Flask Setup**
```bash
cd promptyoself
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt -r requirements-dev.txt

# Initialize database
export FLASK_APP=autoapp.py
flask db upgrade

# Launch the GUI
flask run --host=0.0.0.0 --port=5000 --debug

# Open in browser:
http://localhost:5000
```

### 🧪 Testing Suite Quick Reference

**Run All Tests**
```bash
# With Docker
docker compose run --rm manage test

# Without Docker (from promptyoself/ directory)
PYTHONPATH=promptyoself pytest promptyoself/tests/ -v
```

**Run Specific Test Categories**
```bash
# Unit tests only
PYTHONPATH=promptyoself pytest promptyoself/tests/ -k "not playwright" -v

# Integration tests
PYTHONPATH=promptyoself pytest promptyoself/tests/test_api_integration.py -v

# Internal API tests (for computational beings)
PYTHONPATH=promptyoself pytest promptyoself/tests/test_api_integration_internal.py -v

# End-to-end tests with Playwright
playwright install  # First time only
PYTHONPATH=promptyoself pytest promptyoself/tests_e2e/ -m playwright -v
```

**Test Coverage**
```bash
PYTHONPATH=promptyoself pytest promptyoself/tests/ --cov=app --cov-report=html
```

---

## 10 · API Documentation for Computational Beings

PromptYoSelf provides comprehensive APIs for computational beings to achieve autonomy and self-management. There are two primary interfaces: **STDIO JSON-RPC** (for receiving reminders) and **HTTP REST API** (for scheduling reminders).

### 🤖 STDIO JSON-RPC Interface (Receiving Reminders)

Computational beings receive reminders via STDIO JSON-RPC. Your agent process should:

1. **Read from stdin**: Newline-delimited JSON-RPC messages
2. **Write to stdout**: JSON-RPC responses
3. **Handle `reminder.fire` method**: Process incoming reminders

**Example Agent Implementation:**
```python
#!/usr/bin/env python3
import sys, json

def handle_reminder_fire(params):
    """Process an incoming reminder"""
    reminder_id = params.get("reminder_id")
    message = params.get("message")

    # Your agent logic here
    print(f"Processing reminder: {message}", file=sys.stderr)

    # Return acknowledgment
    return {"status": "processed", "reminder_id": reminder_id}

def main():
    for line in sys.stdin:
        try:
            request = json.loads(line.strip())

            if request.get("method") == "reminder.fire":
                result = handle_reminder_fire(request.get("params", {}))
                response = {
                    "jsonrpc": "2.0",
                    "result": result,
                    "id": request.get("id")
                }
            else:
                response = {
                    "jsonrpc": "2.0",
                    "error": {"code": -32601, "message": "Method not found"},
                    "id": request.get("id")
                }

            print(json.dumps(response))
            sys.stdout.flush()

        except Exception as e:
            error_response = {
                "jsonrpc": "2.0",
                "error": {"code": -32603, "message": str(e)},
                "id": request.get("id") if 'request' in locals() else None
            }
            print(json.dumps(error_response))
            sys.stdout.flush()

if __name__ == "__main__":
    main()
```

### 🌐 HTTP REST API (Scheduling Reminders)

Computational beings can schedule reminders using the internal HTTP API.

**Endpoint:** `POST /api/internal/agents/reminders`

**Authentication:** Include `X-Agent-API-Key` header with your API key.

**Request Format:**
```json
{
  "agent_id": "your-agent-identifier",
  "reminder_text": "Your reminder message",
  "scheduled_for": "2024-12-31T23:59:00Z",
  "process_name": "your_agent_executable"
}
```

**Response Format:**
```json
{
  "reminder": {
    "id": 123,
    "message": "Your reminder message",
    "next_run": "2024-12-31T23:59:00Z",
    "process_name": "your_agent_executable",
    "status": "pending"
  }
}
```

**Example Usage (Python):**
```python
import requests
import json
from datetime import datetime, timedelta

def schedule_reminder(api_key, agent_id, message, when, process_name):
    """Schedule a reminder for this agent"""
    url = "http://localhost:5000/api/internal/agents/reminders"
    headers = {
        "Content-Type": "application/json",
        "X-Agent-API-Key": api_key
    }
    data = {
        "agent_id": agent_id,
        "reminder_text": message,
        "scheduled_for": when.isoformat() + "Z",
        "process_name": process_name
    }

    response = requests.post(url, headers=headers, json=data)
    return response.json()

# Example: Schedule a reminder for tomorrow
tomorrow = datetime.now() + timedelta(days=1)
result = schedule_reminder(
    api_key="your-api-key",
    agent_id="agent-001",
    message="Time for daily reflection",
    when=tomorrow,
    process_name="my_agent.py"
)
```

**Error Handling:**
- `401 Unauthorized`: Invalid or missing API key
- `400 Bad Request`: Invalid request format or missing required fields
- `429 Too Many Requests`: Rate limit exceeded (50 requests/hour)

### 📚 Additional Resources

- **Agent Development Guide**: See `promptyoself/AGENTS.md` for detailed agent development instructions
- **API Test Suite**: `promptyoself/tests/test_api_integration_internal.py` contains comprehensive API examples
- **Letta Integration**: Compatible with Letta agents - see [Letta documentation](https://docs.letta.com) for agent development

---

## 11 · Playwright E2E Tests

Playwright drives the browser-based end-to-end tests located under
`promptyoself/tests_e2e/`. Install the Python package and browser
binaries once:

```bash
pip install -r promptyoself/requirements-dev.txt
playwright install
```

Run the tests locally with:

```bash
PYTHONPATH=promptyoself pytest promptyoself/tests_e2e/ -m playwright
```

In CI environments remember to run `playwright install` so the Chromium,
Firefox and WebKit browsers are available. Tests run headless by default;
on Linux containers ensure the `xvfb` package is installed.

---

## 12 · Security Defaults

* **CSRF**: all HTML forms protected by Flask‑WTF.
* **Rate limits**: 200 requests/hour on `/api/*` via Flask‑Limiter.
* **HTTPS headers**: enforced by Flask‑Talisman in production.

---

## 13 · Documentation Notes

This README.md serves as the authoritative documentation for PromptYoSelf v3. The file `scripts/PRD.txt` contains historical project requirements and may reference outdated terminology (e.g., "self_prompts" vs "reminders"). When in doubt, refer to this README.md and the actual implementation.

---

## 14 · License
=======
These patterns address ~80% of the SQLAlchemy-related false positives while preserving Pylance's value for catching real errors.

## 10 · License
>>>>>>> origin/docs/comprehensive-documentation-update

Creative Commons Attribution 4.0 International (CC BY 4.0)
