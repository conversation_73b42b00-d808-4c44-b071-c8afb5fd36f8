# Type stubs for SQLAlchemy dynamic methods
from typing import Optional, TypeVar, Type, Any

T = TypeVar('T')

class PkModelMixin:
    id: int
    
    @classmethod
    def get_by_id(cls: Type[T], record_id: Any) -> Optional[T]: ...
    
    @classmethod
    def create(cls: Type[T], **kwargs: Any) -> T: ...
    
    def update(self, commit: bool = True, **kwargs: Any) -> 'PkModelMixin': ...
    
    def delete(self, commit: bool = True) -> None: ...
    
    def save(self, commit: bool = True) -> 'PkModelMixin': ...
