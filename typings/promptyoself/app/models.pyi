# Type stubs for promptyoself models
from typing import List, Optional, Any
from datetime import datetime

class PkModel:
    id: int
    query: Any  # SQLAlchemy Query object
    
    @classmethod
    def create(cls, **kwargs: Any) -> 'PkModel': ...

class Project(PkModel):
    name: str
    description: Optional[str]
    created_at: datetime
    updated_at: datetime
    tasks: List['Task']

class Task(PkModel):
    project_id: int
    name: str
    description: Optional[str]
    parent_task_id: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    project: Project
    parent: Optional['Task']
    children: List['Task']
    self_prompts: List['SelfPrompt']

class SelfPrompt(PkModel):
    task_id: int
    message: str
    next_run: datetime
    recurrence: Optional[str]
    event_count: int
    status: str
    process_name: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    task: Task

# Backward compatibility alias
Reminder = SelfPrompt
