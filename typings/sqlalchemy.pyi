# Basic SQLAlchemy type stubs to help with type inference
from typing import Any, List, Optional, TypeVar, Generic
from datetime import datetime

T = TypeVar('T')

class Column:
    def __init__(self, *args: Any, **kwargs: Any) -> None: ...

class Query(Generic[T]):
    def filter(self, *args: Any) -> 'Query[T]': ...
    def join(self, *args: Any) -> 'Query[T]': ...
    def order_by(self, *args: Any) -> 'Query[T]': ...
    def all(self) -> List[T]: ...
    def get(self, ident: Any) -> Optional[T]: ...

class Model:
    id: int
    query: Query[Any]
    
    @classmethod
    def create(cls, **kwargs: Any) -> Any: ...
    
    def save(self) -> Any: ...
    def delete(self) -> Any: ...

# Basic relationship function
def relationship(*args: Any, **kwargs: Any) -> Any: ...
