# Letta Agent Examples for PromptyoSelf Integration

This directory contains example scripts demonstrating how to integrate Letta agents with the PromptyoSelf self-prompt scheduling system.

## Files

### `promptyoself_reminder_tool.py`

A Letta custom tool that allows agents to schedule self-prompts using the PromptyoSelf internal API.

**Features:**
- Schedule self-prompts with specific datetime triggers
- Validate input parameters and datetime formats
- Handle API authentication and error responses
- Provide detailed success/failure feedback

**Usage:**

1. **Set up environment:**
   ```bash
   export INTERNAL_AGENT_API_KEY="your_api_key_here"
   ```

2. **Import and use in Letta agent:**
   ```python
   from promptyoself_reminder_tool import schedule_promptyoself_reminder

   # Schedule a self-prompt
   result = schedule_promptyoself_reminder(
       reminder_text="Follow up on project status",
       scheduled_for="2024-01-15T10:30:00Z",
       process_name="project_management",
       agent_id="my_agent"
   )
   ```

3. **Test the tool standalone:**
   ```bash
   cd examples/agents
   python promptyoself_reminder_tool.py
   ```

**Parameters:**
- `reminder_text` (required): The self-prompt message content
- `scheduled_for` (required): ISO 8601 datetime string (e.g., "2024-01-15T10:30:00Z")
- `process_name` (required): Name of the process scheduling the self-prompt
- `agent_id` (optional): Agent identifier (defaults to "letta_agent")
- `promptyoself_port` (optional): PromptyoSelf server port (defaults to 5000)

**Returns:**
JSON string with either success confirmation including self-prompt ID, or error details.

## Requirements

- `requests` library for HTTP calls
- `INTERNAL_AGENT_API_KEY` environment variable
- PromptyoSelf server running and accessible

## API Integration

The tool integrates with the PromptyoSelf internal API endpoint:
- **Endpoint:** `POST /api/internal/agents/reminders`
- **Authentication:** `X-Agent-API-Key` header
- **Content-Type:** `application/json`

For more details about the API, see the PromptyoSelf documentation.