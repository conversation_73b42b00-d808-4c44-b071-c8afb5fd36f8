#!/usr/bin/env python
"""Initialize the database with all tables."""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import inspect as sqlalchemy_inspect # Corrected import

def init_db(app=None): # init_db is not called directly, init_database is.
    """Initialize the database with all tables."""
    app = create_app()
    
    with app.app_context():
        print("Creating database tables...")
        db.create_all()
        print("✅ Database tables created successfully!")
        
        # List the tables that were created
        inspector = sqlalchemy_inspect(db.engine)
        tables = inspector.get_table_names()
        print(f"📋 Created tables: {', '.join(tables)}")

def init_database(): # This is the function called by if __name__ == '__main__':
    """Initialize the database with all tables."""
    app = create_app()
    
    with app.app_context():
        print("Creating database tables...")
        db.create_all()
        print("✅ Database tables created successfully!")
        
        # List the tables that were created
        inspector = sqlalchemy_inspect(db.engine) # Use the aliased import
        tables = inspector.get_table_names()
        print(f"📋 Created tables: {', '.join(tables)}")

if __name__ == '__main__':
    init_database()
