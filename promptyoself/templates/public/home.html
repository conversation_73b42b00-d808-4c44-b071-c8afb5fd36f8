
{% extends "layout.html" %}
{% block content %}
<!-- <PERSON> jumbotron for a primary marketing message or call to action -->
<div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-20">
    <div class="container mx-auto px-4 text-center">
      <h1 class="text-5xl font-bold mb-4">Welcome to PromptYoSelf</h1>
      <p class="text-xl mb-8 max-w-2xl mx-auto">An app to help computational beings prompt themselves independently. Create reminders, set goals, and stay organized with your personal productivity assistant.</p>
      {% if current_user is defined and current_user.is_authenticated %}
        <a href="{{ url_for('reminders.list_reminders') }}" class="px-8 py-3 bg-white text-indigo-600 font-semibold rounded-lg shadow-md hover:bg-gray-100 transition duration-300">View My Reminders &raquo;</a>
      {% else %}
         <a href="{{ url_for('public.register') }}" class="px-8 py-3 bg-white text-indigo-600 font-semibold rounded-lg shadow-md hover:bg-gray-100 transition duration-300">Get Started &raquo;</a>
      {% endif %}
    </div>
</div>

<div class="container mx-auto px-4 py-16">
  <div class="grid md:grid-cols-3 gap-12 text-center">
    <div class="bg-white p-8 rounded-lg shadow-lg">
      <div class="text-4xl text-indigo-500 mb-4"><i class="fa-solid fa-bell"></i></div>
      <h2 class="text-2xl font-semibold text-gray-800 mb-3">Smart Reminders</h2>
      <p class="text-gray-600 mb-4">Create intelligent reminders with due dates and descriptions. Never forget important tasks again.</p>
      {% if current_user is defined and current_user.is_authenticated %}
        <a class="inline-block px-6 py-2 text-sm font-medium text-indigo-600 border border-indigo-600 rounded-md hover:bg-indigo-600 hover:text-white transition" href="{{ url_for('reminders.new_reminder') }}">Create Reminder &raquo;</a>
      {% endif %}
    </div>
    <div class="bg-white p-8 rounded-lg shadow-lg">
      <div class="text-4xl text-green-500 mb-4"><i class="fa-solid fa-calendar-check"></i></div>
      <h2 class="text-2xl font-semibold text-gray-800 mb-3">Stay Organized</h2>
      <p class="text-gray-600 mb-4">Track your progress with completion status and overdue notifications to keep you on track.</p>
      {% if current_user is defined and current_user.is_authenticated %}
        <a href="{{ url_for('reminders.list_reminders') }}" class="inline-block px-6 py-2 text-sm font-medium text-green-600 border border-green-600 rounded-md hover:bg-green-600 hover:text-white transition">View All &raquo;</a>
      {% endif %}
   </div>
    <div class="bg-white p-8 rounded-lg shadow-lg">
      <div class="text-4xl text-purple-500 mb-4"><i class="fa-solid fa-robot"></i></div>
      <h2 class="text-2xl font-semibold text-gray-800 mb-3">For Computational Beings</h2>
      <p class="text-gray-600 mb-4">Designed specifically for AI agents and computational entities to manage their own tasks and goals.</p>
      {% if current_user is not defined or not current_user.is_authenticated %}
        <a href="{{ url_for('public.register') }}" class="inline-block px-6 py-2 text-sm font-medium text-purple-600 border border-purple-600 rounded-md hover:bg-purple-600 hover:text-white transition">Join Now &raquo;</a>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}

