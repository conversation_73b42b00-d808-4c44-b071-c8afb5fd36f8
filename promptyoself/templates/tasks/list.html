{% extends "layout.html" %}

{% block page_title %}Tasks{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-4 md:mb-0">Tasks</h1>
        <a href="{{ url_for('tasks.new_task') }}" class="w-full md:w-auto px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-center md:ml-auto">New Task</a>
    </div>

    <form method="GET" action="{{ url_for('tasks.list_tasks') }}" class="mb-6 bg-white p-4 rounded-lg shadow">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
            <div>
                <label for="name_filter" class="block text-sm font-medium text-gray-700">Filter by name</label>
                <input type="text" name="name" id="name_filter" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Task name" value="{{ name_filter or '' }}">
            </div>
            <div>
                <label for="project_id_filter" class="block text-sm font-medium text-gray-700">Project</label>
                <select name="project_id" id="project_id_filter" class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="">All Projects</option>
                    {% for p in projects %}
                    <option value="{{ p.id }}" {% if project_id_filter|int == p.id %}selected{% endif %}>{{ p.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="status_filter" class="block text-sm font-medium text-gray-700">Status</label>
                <select name="status" id="status_filter" class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="">All Statuses</option>
                    {% for s_val, s_display in statuses %}
                    <option value="{{ s_val }}" {% if status_filter == s_val %}selected{% endif %}>{{ s_display }}</option>
                    {% endfor %}
                </select>
            </div>
            <button type="submit" class="w-full px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">Filter</button>
        </div>
    </form>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200" role="table" aria-label="Task list">
            <thead class="bg-gray-50" role="rowgroup">
                <tr role="row">
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                    <th scope="col" role="columnheader" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200" role="rowgroup">
                {% for task in paginated_tasks.items %}
                <tr role="row">
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <a href="{{ url_for('tasks.view_task', task_id=task.id) }}" class="text-indigo-600 hover:text-indigo-900">{{ task.name }}</a>
                    </td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <a href="{{ url_for('projects.view_project', project_id=task.project.id) }}" class="text-gray-600 hover:text-gray-900">{{ task.project.name }}</a>
                    </td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ task.status.value if task.status else 'N/A' }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ task.priority.value if task.priority else 'N/A' }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'N/A' }}</td>
                    <td role="cell" class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <a href="{{ url_for('tasks.view_task', task_id=task.id) }}" class="px-3 py-1 text-xs text-white bg-green-500 rounded-md hover:bg-green-600">View</a>
                        <a href="{{ url_for('tasks.edit_task', task_id=task.id) }}" class="px-3 py-1 text-xs text-white bg-blue-500 rounded-md hover:bg-blue-600">Edit</a>
                        <form action="{{ url_for('tasks.delete_task', task_id=task.id) }}" method="POST" class="inline-block">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="px-3 py-1 text-xs text-white bg-red-500 rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this task?');">Delete</button>
                        </form>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No tasks found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if paginated_tasks.pages > 1 %}
    <nav class="mt-6" aria-label="Tasks pagination">
        <ul class="flex justify-center items-center space-x-1" role="list">
            {% if paginated_tasks.has_prev %}
                <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('tasks.list_tasks', page=paginated_tasks.prev_num, name=name_filter, project_id=project_id_filter, status=status_filter) }}">Previous</a></li>
            {% else %}
                <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-50 border border-gray-300 rounded-l-lg cursor-not-allowed">Previous</span></li>
            {% endif %}
    
            {% for page_num in paginated_tasks.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                {% if page_num %}
                    {% if paginated_tasks.page == page_num %}
                        <li role="listitem"><span class="px-3 py-2 leading-tight text-indigo-600 bg-indigo-50 border border-indigo-300 hover:bg-indigo-100 hover:text-indigo-700 z-10" aria-current="page">{{ page_num }}</span></li>
                    {% else %}
                        <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('tasks.list_tasks', page=page_num, name=name_filter, project_id=project_id_filter, status=status_filter) }}">{{ page_num }}</a></li>
                    {% endif %}
                {% else %}
                     <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300">...</span></li>
                {% endif %}
            {% endfor %}
    
            {% if paginated_tasks.has_next %}
                <li role="listitem"><a class="px-3 py-2 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700" href="{{ url_for('tasks.list_tasks', page=paginated_tasks.next_num, name=name_filter, project_id=project_id_filter, status=status_filter) }}">Next</a></li>
            {% else %}
                <li role="listitem"><span class="px-3 py-2 leading-tight text-gray-400 bg-gray-50 border border-gray-300 rounded-r-lg cursor-not-allowed">Next</span></li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}