{% extends "layout.html" %}
{% from "macros/form_helpers.html" import render_field %}

{% block page_title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-2xl">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">{{ title }}</h1>
    <form method="POST" action="{{ request.path }}" class="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-4" role="form">
        {{ form.hidden_tag() }}
        {{ render_field(form.name, placeholder="Enter task name") }}
        {{ render_field(form.project_id) }}
        {% if form.parent_task_id %}
            {{ render_field(form.parent_task_id) }}
        {% endif %}
        {{ render_field(form.description, placeholder="Enter task description", rows=5) }}
        {{ render_field(form.status) }}
        {{ render_field(form.priority) }}
        {{ render_field(form.due_date, type="date") }}
        
        <div class="flex items-center justify-end mt-6 space-x-3">
            <a href="{{ url_for('tasks.list_tasks', project_id=form.project_id.data if form.project_id.data else None) }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">Cancel</a>
            <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">Save Task</button>
        </div>
    </form>
</div>
{% endblock %}