{% extends "layout.html" %}

{% block page_title %}Task Details: {{ task.name }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <a href="{{ url_for('tasks.list_tasks', project_id=task.project_id) }}" class="text-indigo-600 hover:text-indigo-800 text-sm mr-4">&larr; Back to Tasks for {{task.project.name}}</a>
        <a href="{{ url_for('tasks.list_tasks') }}" class="text-indigo-600 hover:text-indigo-800 text-sm">&larr; Back to All Tasks</a>
    </div>
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Task: {{ task.name }}</h1>

    <div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-700">Task Information</h2>
        </div>
        <div class="px-6 py-4">
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">ID</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ task.id }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Name</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ task.name }}</dd>
                </div>
                <div class="sm:col-span-2">
                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ task.description }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Project</dt>
                    <dd class="mt-1 text-sm text-gray-900"><a href="{{ url_for('projects.view_project', project_id=task.project.id) }}" class="text-indigo-600 hover:text-indigo-800">{{ task.project.name }}</a></dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Status</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ task.status.value if task.status else 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Priority</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ task.priority.value if task.priority else 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Due Date</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Created At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ task.created_at.strftime('%Y-%m-%d %H:%M') if task.created_at else 'N/A' }}</dd>
                </div>
                <div class="sm:col-span-1">
                    <dt class="text-sm font-medium text-gray-500">Updated At</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ task.updated_at.strftime('%Y-%m-%d %H:%M') if task.updated_at else 'N/A' }}</dd>
                </div>
            </dl>
        </div>
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 justify-end">
            <a href="{{ url_for('tasks.edit_task', task_id=task.id) }}" class="px-4 py-2 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 text-center">Edit Task</a>
            <form action="{{ url_for('tasks.delete_task', task_id=task.id) }}" method="POST" class="inline-block">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                <button type="submit" class="w-full sm:w-auto px-4 py-2 bg-red-500 text-white text-sm rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this task?');">Delete Task</button>
            </form>
        </div>
    </div>

    <div class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-semibold text-gray-700">Associated Reminders</h2>
            <a href="{{ url_for('reminders.new_reminder', task_id=task.id) }}" class="px-4 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600">Add New Reminder</a>
        </div>
        {% if task.reminders %}
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Run</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recurrence</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for reminder in task.reminders %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ url_for('reminders.view_reminder', reminder_id=reminder.id) }}" class="text-indigo-600 hover:text-indigo-900">{{ reminder.message|truncate(50) }}</a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ reminder.next_run.strftime('%Y-%m-%d %H:%M') if reminder.next_run else 'N/A' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ reminder.recurrence or 'N/A' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="{{ url_for('reminders.view_reminder', reminder_id=reminder.id) }}" class="px-3 py-1 text-xs text-white bg-green-500 rounded-md hover:bg-green-600">View</a>
                                <a href="{{ url_for('reminders.edit_reminder', reminder_id=reminder.id) }}" class="px-3 py-1 text-xs text-white bg-blue-500 rounded-md hover:bg-blue-600">Edit</a>
                                <form action="{{ url_for('reminders.delete_reminder', reminder_id=reminder.id) }}" method="POST" class="inline-block">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                    <button type="submit" class="px-3 py-1 text-xs text-white bg-red-500 rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this reminder?');">Delete</button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="bg-white shadow-md rounded-lg p-6 text-center text-gray-500">
                <p>No reminders associated with this task yet.</p>
            </div>
        {% endif %}
    </div>

    <div>
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-semibold text-gray-700">Subtasks</h2>
            <a href="{{ url_for('tasks.new_task', parent_id=task.id, project_id=task.project_id) }}" class="px-4 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600">Add New Subtask</a>
        </div>
        {% if task.children %}
            <div class="bg-white shadow-md rounded-lg overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtask Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for subtask in task.children %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ url_for('tasks.view_task', task_id=subtask.id) }}" class="text-indigo-600 hover:text-indigo-900">{{ subtask.name }}</a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ subtask.status.value if subtask.status else 'N/A' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ subtask.priority.value if subtask.priority else 'N/A' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ subtask.due_date.strftime('%Y-%m-%d') if subtask.due_date else 'N/A' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="{{ url_for('tasks.view_task', task_id=subtask.id) }}" class="px-3 py-1 text-xs text-white bg-green-500 rounded-md hover:bg-green-600">View</a>
                                <a href="{{ url_for('tasks.edit_task', task_id=subtask.id) }}" class="px-3 py-1 text-xs text-white bg-blue-500 rounded-md hover:bg-blue-600">Edit</a>
                                <form action="{{ url_for('tasks.delete_task', task_id=subtask.id) }}" method="POST" class="inline-block">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                    <button type="submit" class="px-3 py-1 text-xs text-white bg-red-500 rounded-md hover:bg-red-600" onclick="return confirm('Are you sure you want to delete this subtask?');">Delete</button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
             <div class="bg-white shadow-md rounded-lg p-6 text-center text-gray-500">
                <p>No subtasks associated with this task yet.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}