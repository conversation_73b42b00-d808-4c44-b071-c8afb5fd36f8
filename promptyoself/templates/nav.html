{% macro nav_link(endpoint, url_name, label, classes='px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700',
active_class='bg-gray-900') %}
{% set ep = request.endpoint|default('') %}
<a href="{{ url_for(url_name) }}"
  class="{{ classes }} {% if ep == endpoint or (endpoint.endswith('.') and ep.startswith(endpoint)) %}{{ active_class }}{% endif %}">
  {{ label }}
</a>
{% endmacro %}

{% macro nav_link_mobile(endpoint, url_name, label, classes='block px-3 py-2 rounded-md text-base font-medium
hover:bg-gray-700', active_class='bg-gray-900') %}
{% set ep = request.endpoint|default('') %}
<a href="{{ url_for(url_name) }}"
  class="{{ classes }} {% if ep == endpoint or (endpoint.endswith('.') and ep.startswith(endpoint)) %}{{ active_class }}{% endif %}">
  {{ label }}
</a>
{% endmacro %}
<div x-data="{ open: false }">
  <nav class="bg-gray-800 text-white fixed top-0 left-0 right-0 z-50 shadow-md">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center">
          <a class="text-2xl font-bold hover:text-gray-300" href="{{ url_for('public.home') }}">
            PromptYoSelf
          </a>
        </div>
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            {{ nav_link('public.home', 'public.home', 'Home') }}
            {{ nav_link('public.about', 'public.about', 'About') }}
            {{ nav_link('projects.', 'projects.list_projects', 'Projects') }}
            {{ nav_link('tasks.', 'tasks.list_tasks', 'Tasks') }}
            {{ nav_link('reminders.', 'reminders.list_reminders', 'Reminders') }}

            {{ nav_link('projects.new_project', 'projects.new_project', 'New Project',
            'px-3 py-2 rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700',
            'bg-green-700 ring-2 ring-green-300') }}
            {{ nav_link('tasks.new_task', 'tasks.new_task', 'New Task',
            'px-3 py-2 rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700',
            'bg-blue-700 ring-2 ring-blue-300') }}
            {{ nav_link('reminders.new_reminder', 'reminders.new_reminder', 'New Reminder',
            'px-3 py-2 rounded-md text-sm font-medium text-white bg-yellow-500 hover:bg-yellow-600',
            'bg-yellow-600 ring-2 ring-yellow-300') }}
          </div>
        </div>
        <div class="hidden md:block">
          <div class="ml-4 flex items-center md:ml-6">
            <a href="{{ url_for('public.register') }}"
              class="px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700">Create User</a>
          </div>
        </div>
        <div class="-mr-2 flex md:hidden">
          <!-- Mobile menu button -->
          <button @click="open = !open" type="button"
            class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
            aria-controls="mobile-menu" aria-expanded="false">
            <span class="sr-only">Open main menu</span>
            <svg :class="{'hidden': open, 'block': !open }" class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg"
              fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            <svg :class="{'hidden': !open, 'block': open }" class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg"
              fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div class="md:hidden" id="mobile-menu" x-show="open" @click.away="open = false"
      x-transition:enter="transition ease-out duration-100 transform" x-transition:enter-start="opacity-0 scale-95"
      x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-75 transform"
      x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-95">
      <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
        {{ nav_link_mobile('public.home', 'public.home', 'Home') }}
        {{ nav_link_mobile('public.about', 'public.about', 'About') }}
        {{ nav_link_mobile('projects.', 'projects.list_projects', 'Projects') }}
        {{ nav_link_mobile('tasks.', 'tasks.list_tasks', 'Tasks') }}
        {{ nav_link_mobile('reminders.', 'reminders.list_reminders', 'Reminders') }}
        <hr class="border-gray-600 my-2">
        {{ nav_link_mobile('projects.new_project', 'projects.new_project', 'New Project',
        'block px-3 py-2 rounded-md text-base font-medium text-white bg-green-600 hover:bg-green-700',
        'bg-green-700 ring-1 ring-green-300') }}
        {{ nav_link_mobile('tasks.new_task', 'tasks.new_task', 'New Task',
        'block px-3 py-2 rounded-md text-base font-medium text-white bg-blue-600 hover:bg-blue-700',
        'bg-blue-700 ring-1 ring-blue-300') }}
        {{ nav_link_mobile('reminders.new_reminder', 'reminders.new_reminder', 'New Reminder',
        'block px-3 py-2 rounded-md text-base font-medium text-white bg-yellow-500 hover:bg-yellow-600',
        'bg-yellow-600 ring-1 ring-yellow-300') }}
      </div>
      <div class="pt-4 pb-3 border-t border-gray-700">
        <div class="px-2 space-y-1">
          <a href="{{ url_for('public.register') }}"
            class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-white hover:bg-gray-700">Create
            User</a>
        </div>
      </div>
    </div>
  </nav>
</div>
<div class="h-16"></div> <!-- Spacer for fixed navbar -->