# -*- coding: utf-8 -*-
"""Create an application instance."""
import atexit
import subprocess
from promptyoself.app import create_app

app = create_app()


def kill_port_5000():
    """Kill any processes using port 5000 on shutdown."""
    try:
        # Use lsof to find and kill processes on port 5000
        subprocess.run(
            ["lsof", "-ti", ":5000", "|", "xargs", "kill", "-9"],
            shell=True,
            check=True
        )
        print("Successfully killed processes on port 5000")
    except Exception as e:
        print(f"Error killing processes on port 5000: {e}")


# Register the cleanup function to run on exit
atexit.register(kill_port_5000)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
