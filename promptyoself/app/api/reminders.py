# -*- coding: utf-8 -*-
"""API endpoints for self-prompts."""
from flask import Blueprint, request, jsonify, current_app
from werkzeug.exceptions import BadRequest, NotFound

from ..forms import ReminderForm
# Using backward compatibility alias
from ..models import <PERSON>minder, Task, Project
from ..extensions import db, limiter  # Added limiter

blueprint = Blueprint("api_self_prompts", __name__,
                      url_prefix="/api/self-prompts")


@blueprint.route("/", methods=["GET"])
@limiter.limit("100 per hour")  # Added rate limit
def list_reminders():
    """List all reminders via API."""
    reminders = Reminder.query.join(Task).join(Project).order_by(
        Project.name, Task.name, Reminder.next_run).all()  # type: ignore

    return jsonify({
        "reminders": [
            {
                "id": reminder.id,
                "message": reminder.message,
                "next_run": reminder.next_run.isoformat(),
                "recurrence": reminder.recurrence,
                "process_name": reminder.process_name,
                "task_id": reminder.task_id,
                "task_name": reminder.task.name if reminder.task else None,
                "project_name": reminder.task.project.name if reminder.task and reminder.task.project else None,
                "created_at": reminder.created_at.isoformat(),
                "updated_at": reminder.updated_at.isoformat(),
            }
            for reminder in reminders
        ]
    }), 200


@blueprint.route("/", methods=["POST"])
@limiter.limit("50 per hour")  # Added rate limit
def create_reminder():
    """Create a new reminder via API."""
    if not request.is_json:
        raise BadRequest("Content-Type must be application/json")

    data = request.get_json()
    form = ReminderForm(data=data)

    if form.validate():
        reminder = Reminder.create(  # type: ignore
            message=form.message.data,
            next_run=form.next_run.data,
            recurrence=form.recurrence.data or None,
            process_name=form.process_name.data,
            task_id=form.task_id.data
        )
        current_app.logger.info(
            f"Reminder created via API for task: {reminder.task.name}")

        return jsonify({
            "message": "Reminder created successfully",
            "reminder": {
                "id": reminder.id,
                "message": reminder.message,
                "next_run": reminder.next_run.isoformat(),
                "recurrence": reminder.recurrence,
                "process_name": reminder.process_name,
                "task_id": reminder.task_id,
                "task_name": reminder.task.name if reminder.task else None,
                "project_name": reminder.task.project.name if reminder.task and reminder.task.project else None,
                "created_at": reminder.created_at.isoformat(),
                "updated_at": reminder.updated_at.isoformat(),
            }
        }), 201
    else:
        return jsonify({"errors": form.errors}), 400


@blueprint.route("/<int:reminder_id>", methods=["GET"])
@limiter.limit("100 per hour")  # Added rate limit
def get_reminder(reminder_id: int):
    """Get a specific reminder via API."""
    reminder = Reminder.query.get(reminder_id)  # type: ignore

    if not reminder:
        raise NotFound("Reminder not found")

    return jsonify({
        "reminder": {
            "id": reminder.id,
            "message": reminder.message,
            "next_run": reminder.next_run.isoformat(),
            "recurrence": reminder.recurrence,
            "process_name": reminder.process_name,
            "task_id": reminder.task_id,
            "task_name": reminder.task.name if reminder.task else None,
            "project_name": reminder.task.project.name if reminder.task and reminder.task.project else None,
            "created_at": reminder.created_at.isoformat(),
            "updated_at": reminder.updated_at.isoformat(),
        }
    }), 200


@blueprint.route("/<int:reminder_id>", methods=["PUT"])
@limiter.limit("50 per hour")  # Added rate limit
def update_reminder(reminder_id: int):
    """Update a reminder via API."""
    if not request.is_json:
        raise BadRequest("Content-Type must be application/json")

    reminder = Reminder.query.get(reminder_id)  # type: ignore
    if not reminder:
        raise NotFound("Reminder not found")

    data = request.get_json()
    form = ReminderForm(data=data, obj=reminder)

    if form.validate():
        form.populate_obj(reminder)
        db.session.commit()
        current_app.logger.info(
            f"Reminder updated via API for task: {reminder.task.name}")

        return jsonify({
            "message": "Reminder updated successfully",
            "reminder": {
                "id": reminder.id,
                "message": reminder.message,
                "next_run": reminder.next_run.isoformat(),
                "recurrence": reminder.recurrence,
                "process_name": reminder.process_name,
                "task_id": reminder.task_id,
                "task_name": reminder.task.name if reminder.task else None,
                "project_name": reminder.task.project.name if reminder.task and reminder.task.project else None,
                "created_at": reminder.created_at.isoformat(),
                "updated_at": reminder.updated_at.isoformat(),
            }
        }), 200
    else:
        return jsonify({"errors": form.errors}), 400


@blueprint.route("/<int:reminder_id>", methods=["DELETE"])
@limiter.limit("50 per hour")  # Added rate limit
def delete_reminder(reminder_id: int):
    """Delete a reminder via API."""
    reminder = Reminder.query.get(reminder_id)  # type: ignore
    if not reminder:
        raise NotFound("Reminder not found")

    task_name = reminder.task.name if reminder.task else "N/A"
    db.session.delete(reminder)
    db.session.commit()
    current_app.logger.info(f"Reminder deleted via API for task: {task_name}")

    return jsonify({"message": f"Reminder for task '{task_name}' deleted successfully"}), 200

# Removed complete_reminder endpoint as 'completed' status is not part of the new Reminder model
