# -*- coding: utf-8 -*-
"""API endpoints for internal use by managed agents."""
from functools import wraps
from datetime import datetime
from flask import Blueprint, jsonify, request, current_app
from werkzeug.exceptions import Unauthorized, BadRequest
from sqlalchemy.exc import IntegrityError, DataError, OperationalError, SQLAlchemyError
from ..extensions import limiter, db
from ..models import Reminder, Task, Project

blueprint = Blueprint("internal_api", __name__, url_prefix="/api/internal")


def require_agent_api_key(f):
    """
    Decorator to enforce API key authentication for internal agent endpoints.
    Expects the API key in the X-Agent-API-Key header.
    Returns 401 Unauthorized if the API key is missing or invalid.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check if API key is configured
        configured_api_key = current_app.config.get('INTERNAL_AGENT_API_KEY')
        if not configured_api_key:
            current_app.logger.error(
                "INTERNAL_AGENT_API_KEY is not configured")
            return jsonify({"error": "Server misconfiguration - API key not set"}), 500

        # Get API key from header
        api_key = request.headers.get("X-Agent-API-Key")
        if not api_key:
            current_app.logger.warning(
                "Request missing X-Agent-API-Key header")
            return jsonify({"error": "Authentication required - missing API key"}), 401

        # Validate API key
        if api_key != configured_api_key:
            current_app.logger.warning("Invalid API key provided")
            return jsonify({"error": "Authentication failed - invalid API key"}), 401

        return f(*args, **kwargs)
    return decorated_function


@blueprint.route("/agents/reminders", methods=["POST"])
@require_agent_api_key
@limiter.limit("50 per hour")  # Rate limit for security
def create_agent_reminder():
    """
    Create a new reminder via internal API for managed agents.
    This endpoint allows trusted agents (e.g., Letta) to schedule reminders programmatically.

    Expected JSON payload:
    {
        "agent_id": "string/int",      # ID of the agent scheduling the reminder
        "reminder_text": "string",     # Text content of the reminder (required, non-empty)
        "scheduled_for": "string",     # ISO 8601 formatted datetime when reminder should trigger
        "process_name": "string"       # Process name (required, non-empty)
    }

    Returns:
        - 400 Bad Request: If validation fails (with specific error message)
        - 201 Created: If validation succeeds
    """
    # Check if request contains JSON data
    if not request.is_json:
        current_app.logger.warning(
            "Request to internal reminder API missing Content-Type: application/json")
        return jsonify({"error": "Content-Type must be application/json"}), 400

    # Get JSON data from request
    data = request.get_json()

    # Validate required fields are present
    required_fields = ["agent_id", "reminder_text",
                       "scheduled_for", "process_name"]
    for field in required_fields:
        if field not in data:
            current_app.logger.warning(
                f"Missing required field in internal reminder API request: {field}")
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Validate field types and formats

    # Check reminder_text is non-empty string
    if not isinstance(data["reminder_text"], str) or not data["reminder_text"].strip():
        return jsonify({"error": "reminder_text must be a non-empty string"}), 400

    # Check process_name is non-empty string (explicit NOT NULL check)
    if not isinstance(data["process_name"], str) or not data["process_name"].strip():
        return jsonify({"error": "process_name must be a non-empty string"}), 400

    # Check scheduled_for is a valid ISO 8601 datetime
    try:
        scheduled_datetime = datetime.fromisoformat(
            data["scheduled_for"].replace('Z', '+00:00'))
    except (ValueError, TypeError):
        return jsonify({"error": "scheduled_for must be a valid ISO 8601 datetime string"}), 400

    # Check agent_id is present (can be string or int)
    if not data["agent_id"]:
        return jsonify({"error": "agent_id must not be empty"}), 400

    # If we get here, validation has passed
    current_app.logger.info(
        f"Valid reminder payload received from agent ID: {data['agent_id']}")

    try:
        # Get or create a system project for agent reminders
        system_project = Project.query.filter_by(name="Agent System").first()
        if not system_project:
            system_project = Project.create(
                name="Agent System",
                description="System project for agent-related activities"
            )
            current_app.logger.info(
                "Created system project for agent reminders")

        # Get or create a system task for agent reminders
        system_task = Task.query.filter_by(
            name="Agent Scheduled Reminders",
            project_id=system_project.id
        ).first()

        if not system_task:
            system_task = Task.create(
                project_id=system_project.id,
                name="Agent Scheduled Reminders",
                description=f"System task for reminders scheduled by agents"
            )
            current_app.logger.info("Created system task for agent reminders")

        # Create the reminder
        new_reminder = Reminder(
            task_id=system_task.id,
            message=data["reminder_text"],
            next_run=scheduled_datetime,
            process_name=data["process_name"],
            status="pending",
            event_count=0
        )

        # Save to database
        new_reminder.save()

        current_app.logger.info(
            f"Agent ID: {data['agent_id']} created new reminder with ID: {new_reminder.id}")

        # Return success response
        return jsonify({
            "message": "Reminder created successfully",
            "reminder": {
                "id": new_reminder.id,
                "agent_id": data["agent_id"],
                "message": new_reminder.message,
                "scheduled_for": new_reminder.next_run.isoformat(),
                "process_name": new_reminder.process_name,
                "status": new_reminder.status
            }
        }), 201

    except IntegrityError as e:
        db.session.rollback()
        current_app.logger.error(
            f"Database integrity error creating reminder for agent ID: {data.get('agent_id', 'unknown')}: {str(e)}")
        return jsonify({"error": "Database constraint violation. This could be due to a duplicate entry or invalid reference."}), 409
    except DataError as e:
        db.session.rollback()
        current_app.logger.error(
            f"Database data error creating reminder for agent ID: {data.get('agent_id', 'unknown')}: {str(e)}")
        return jsonify({"error": "Invalid data for database. The provided values don't match the expected data types."}), 422
    except OperationalError as e:
        db.session.rollback()
        current_app.logger.error(
            f"Database operational error creating reminder for agent ID: {data.get('agent_id', 'unknown')}: {str(e)}")
        error_msg = str(e).lower()
        # Only return 503 for genuine service outages (e.g. database cannot be opened)
        if "unable to open" in error_msg or "could not connect" in error_msg:
            return jsonify({"error": "Database operation failed. The service may be temporarily unavailable."}), 503
        # For other operational errors treat it as a server error
        return jsonify({"error": "Database error occurred while saving reminder."}), 500
    except SQLAlchemyError as e:
        db.session.rollback()
        current_app.logger.error(
            f"Database error creating reminder for agent ID: {data.get('agent_id', 'unknown')}: {str(e)}")
        return jsonify({"error": "Database error occurred while saving reminder."}), 500
    except Exception as e:
        # Catch-all for any other unexpected errors
        if db.session.is_active:
            db.session.rollback()
        current_app.logger.error(
            f"Unexpected error creating reminder for agent ID: {data.get('agent_id', 'unknown')}: {str(e)}", exc_info=True)
        return jsonify({"error": "An unexpected error occurred while processing your request."}), 500
