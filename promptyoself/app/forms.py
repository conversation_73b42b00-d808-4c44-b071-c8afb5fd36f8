# -*- coding: utf-8 -*-
"""Application forms."""
from flask_wtf import FlaskForm
from wtforms import PasswordField, StringField, TextAreaField, DateTimeField, SelectField, IntegerField, HiddenField, DateField
from wtforms.validators import <PERSON>Required, Email, EqualTo, Length, Optional, NumberRange
from wtforms.widgets import TextArea

from .models import User, Project, Task


class LoginForm(FlaskForm):
    """Login form."""

    username = StringField("Username", validators=[DataRequired()])
    password = PasswordField("Password", validators=[DataRequired()])

    def __init__(self, *args, **kwargs):
        """Create instance."""
        super(LoginForm, self).__init__(*args, **kwargs)
        self.user = None

    def validate(self, **kwargs):
        """Validate the form."""
        initial_validation = super(<PERSON>ginForm, self).validate()
        if not initial_validation:
            return False

        self.user = User.query.filter_by(username=self.username.data).first()
        if not self.user:
            self.username.errors.append("Unknown username")
            return False

        if not self.user.check_password(self.password.data):
            self.password.errors.append("Invalid password")
            return False

        if not self.user.active:
            self.username.errors.append("User not activated")
            return False
        return True


class RegisterForm(FlaskForm):
    """Register form."""

    username = StringField(
        "Username", validators=[DataRequired(), Length(min=3, max=25)]
    )
    email = StringField(
        "Email", validators=[DataRequired(), Email(), Length(min=6, max=40)]
    )
    password = PasswordField(
        "Password", validators=[DataRequired(), Length(min=6, max=40)]
    )
    confirm = PasswordField(
        "Verify password",
        [DataRequired(), EqualTo("password", message="Passwords must match")],
    )

    def __init__(self, *args, **kwargs):
        """Create instance."""
        super(RegisterForm, self).__init__(*args, **kwargs)
        self.user = None

    def validate(self, **kwargs):
        """Validate the form."""
        initial_validation = super(RegisterForm, self).validate()
        if not initial_validation:
            return False
        user = User.query.filter_by(username=self.username.data).first()
        if user:
            self.username.errors.append("Username already registered")
            return False
        user = User.query.filter_by(email=self.email.data).first()
        if user:
            self.email.errors.append("Email already registered")
            return False
        return True


class ProjectForm(FlaskForm):
    """Project form."""

    name = StringField(
        "Project Name", validators=[DataRequired(), Length(min=1, max=255)]
    )
    description = TextAreaField("Description", validators=[Optional()])

    def __init__(self, *args, **kwargs):
        """Create instance."""
        super(ProjectForm, self).__init__(*args, **kwargs)


class TaskForm(FlaskForm):
    """Task form."""

    name = StringField(
        "Task Name", validators=[DataRequired(), Length(min=1, max=255)]
    )
    description = TextAreaField("Description", validators=[Optional()])
    project_id = SelectField(
        "Project",
        validators=[DataRequired()],
        coerce=int,
        choices=[]
    )
    parent_task_id = SelectField(
        "Parent Task",
        validators=[Optional()],
        coerce=lambda x: int(x) if x else None,
        choices=[]
    )
    status = SelectField(
        "Status",
        validators=[Optional()],
        choices=[
            ('', 'Select Status'),
            ('not_started', 'Not Started'),
            ('in_progress', 'In Progress'),
            ('completed', 'Completed'),
            ('blocked', 'Blocked')
        ]
    )
    priority = SelectField(
        "Priority",
        validators=[Optional()],
        choices=[
            ('', 'Select Priority'),
            ('low', 'Low'),
            ('medium', 'Medium'),
            ('high', 'High')
        ]
    )
    due_date = DateField(
        "Due Date",
        validators=[Optional()],
        format='%Y-%m-%d'
    )

    def __init__(self, *args, **kwargs):
        """Create instance."""
        super(TaskForm, self).__init__(*args, **kwargs)

        # Populate project choices
        self.project_id.choices = [(p.id, p.name) for p in Project.query.all()]

        # Populate parent task choices (only for the selected project)
        self.parent_task_id.choices = [(None, 'No Parent Task')]
        if hasattr(self, 'project_id') and self.project_id.data:
            tasks = Task.query.filter_by(project_id=self.project_id.data).all()
            self.parent_task_id.choices.extend([(t.id, t.name) for t in tasks])


class ReminderForm(FlaskForm):
    """Reminder form."""

    message = TextAreaField(
        "Message", validators=[DataRequired(), Length(min=1, max=500)]
    )
    next_run = DateTimeField(
        "Next Run", validators=[DataRequired()], format='%Y-%m-%d %H:%M'
    )
    recurrence = SelectField(
        "Recurrence",
        choices=[
            ('', 'No Recurrence'),
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly')
        ],
        validators=[Optional()]
    )
    process_name = StringField(
        "Process Name", validators=[DataRequired(), Length(min=1, max=255)]
    )
    task_id = SelectField(
        "Task",
        validators=[DataRequired()],
        coerce=int,
        choices=[]
    )

    def __init__(self, *args, **kwargs):
        """Create instance."""
        super(ReminderForm, self).__init__(*args, **kwargs)

        # Populate task choices with project context
        tasks = Task.query.join(Project).all()
        self.task_id.choices = [
            (t.id, f"{t.project.name} - {t.name}") for t in tasks
        ]
