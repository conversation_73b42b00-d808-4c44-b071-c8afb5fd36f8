# -*- coding: utf-8 -*-
"""Public UI views, including homepage and simplified user creation."""
from flask import (
    Blueprint,
    current_app,
    flash,
    redirect,
    render_template,
    request,
    url_for,
)
from flask_login import login_user

from ..forms import RegisterForm
from ..models import User
from ..utils import flash_errors

blueprint = Blueprint("public", __name__, static_folder="../static")


@blueprint.route("/")
def home():
    """Home page."""
    current_app.logger.info("Hello from the home page!")
    return render_template("public/home.html")


@blueprint.route("/register/", methods=["GET", "POST"])
def register():
    """Create new user (simplified - no password/login)."""
    form = RegisterForm(request.form)
    if form.validate_on_submit():
        user = User.create(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data,  # Keep for now to avoid form validation issues
            active=True,
        )
        login_user(user)
        flash("User created successfully and logged in.", "success")
        return redirect(url_for("public.home"))
    else:
        flash_errors(form)
    return render_template("public/register.html", form=form)


@blueprint.route("/about/")
def about():
    """About page."""
    return render_template("public/about.html")
