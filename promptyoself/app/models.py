# -*- coding: utf-8 -*-
"""Application models."""
import datetime as dt
from typing import TYPE_CHECKING, Optional, List  # Added for type hints

from flask_login import UserMixin
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped  # Import Mapped for type hints

from .database import Column, PkModel, db, reference_col, relationship
from .extensions import bcrypt


class Role(PkModel):
    """A role for a user."""

    __tablename__ = "roles"
    __table_args__ = {'extend_existing': True}
    __allow_unmapped__ = True  # Allow legacy annotations
    name: str = Column(db.String(80), unique=True, nullable=False)
    user_id: Optional[int] = reference_col("users", nullable=True)
    user: 'User' # type: ignore # Using string literal and type: ignore for Pylance
    # user: Mapped[Optional["User"]] = relationship("User", backref="roles")

    def __init__(self, name: str, **kwargs):
        """Create instance."""
        super().__init__(**kwargs)
        self.name = name

    def __repr__(self):
        """Represent instance as a unique string."""
        return f"<Role({self.name})>"


class User(UserMixin, PkModel):
    """A user of the app.

    User Identification:
    The User model inherits an auto-incrementing primary key 'id' field from PkModel.
    This 'id' field serves as the unique user identifier (user_id) throughout the
    application. When referencing a user in relationships, session management, or
    API operations, use user.id or current_user.id to access the user_id.

    No separate user_id field is needed as the inherited 'id' field fulfills this role.
    """

    __tablename__ = "users"
    __table_args__ = {'extend_existing': True}
    __allow_unmapped__ = True  # Allow legacy annotations
    username: str = Column(db.String(80), unique=True, nullable=False)
    email: str = Column(db.String(80), unique=True, nullable=False)
    # _password = Column("password", db.LargeBinary(128), nullable=True) # Original field
    _password: Mapped[Optional[bytes]] = Column("password", db.LargeBinary(128), nullable=True) # Reverted to _password, Pylance issue with setter might be separate

    created_at: dt.datetime = Column(
        db.DateTime, nullable=False, default=dt.datetime.now(dt.timezone.utc)
    )
    first_name: Optional[str] = Column(db.String(30), nullable=True)
    last_name: Optional[str] = Column(db.String(30), nullable=True)
    active: bool = Column(db.Boolean(), default=False)
    is_admin: bool = Column(db.Boolean(), default=False)

    # For relationship type hint if needed by Pylance
    if TYPE_CHECKING:
        roles: Mapped[List["Role"]]

    @hybrid_property
    def password(self) -> Optional[bytes]:
        """Hashed password."""
        return self._password # Use _password

    @password.setter
    def password(self, value: str): # Setter expects string
        """Set password."""
        self._password = bcrypt.generate_password_hash(value) # Use _password

    def check_password(self, value: str) -> bool:
        """Check password."""
        if self._password is None:
            return False
        return bcrypt.check_password_hash(self._password, value) # Use _password

    @property
    def full_name(self) -> str:
        """Full user name."""
        return f"{self.first_name or ''} {self.last_name or ''}".strip()

    def __repr__(self):
        """Represent instance as a unique string."""
        return f"<User({self.username!r})>"


class Project(PkModel):
    """A project."""

    __tablename__ = "projects"
    __table_args__ = {'extend_existing': True}
    __allow_unmapped__ = True  # Allow legacy annotations
    name: str = Column(db.String(255), nullable=False)
    description: Optional[str] = Column(db.Text, nullable=True)
    created_at: dt.datetime = Column(
        db.DateTime, nullable=False, default=db.func.now())
    updated_at: dt.datetime = Column(db.DateTime, nullable=False,
                                     default=db.func.now(), onupdate=db.func.now())

    tasks = db.relationship(  # type: ignore
        "Task",
        backref=db.backref("project", lazy=True),
        lazy=True,
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        """Represent instance as a unique string."""
        return f"<Project({self.name!r})>"


class Task(PkModel):
    """A task within a project."""

    __tablename__ = "tasks"
    __table_args__ = {'extend_existing': True}
    __allow_unmapped__ = True  # Allow legacy annotations
    project_id: int = Column(db.Integer, db.ForeignKey(
        "projects.id"), nullable=False)
    name: str = Column(db.String(255), nullable=False)
    description: Optional[str] = Column(db.Text, nullable=True)
    parent_task_id: Optional[int] = Column(
        db.Integer, db.ForeignKey("tasks.id"), nullable=True)
    created_at: dt.datetime = Column(
        db.DateTime, nullable=False, default=db.func.now())
    updated_at: dt.datetime = Column(db.DateTime, nullable=False,
                                     default=db.func.now(), onupdate=db.func.now())

    # Simplified type hints to avoid SQLAlchemy issues
    parent = db.relationship(  # type: ignore
        "Task",
        remote_side="Task.id",  # Use string reference
        backref=db.backref("children", lazy=True,
                           cascade="all, delete-orphan"),
        foreign_keys=[parent_task_id],  # Use the column object directly
        lazy=True,
    )

    # Relationship to SelfPrompt (Reminder)
    self_prompts = db.relationship(  # type: ignore
        "SelfPrompt",
        backref=db.backref("task", lazy=True),
        lazy=True,
        cascade="all, delete-orphan",
    )

    if TYPE_CHECKING:
        children: Mapped[List["Task"]]
        self_prompts: Mapped[List["SelfPrompt"]]
        project: Mapped["Project"]

    def __repr__(self):
        """Represent instance as a unique string."""
        return f"<Task({self.name!r})>"


class SelfPrompt(PkModel):
    """A self-prompt for a task."""

    __tablename__ = "self_prompts"
    __table_args__ = {'extend_existing': True}
    __allow_unmapped__ = True  # Allow legacy annotations
    task_id: int = Column(
        db.Integer, db.ForeignKey("tasks.id"), nullable=False)
    message: str = Column(db.Text, nullable=False)
    next_run: dt.datetime = Column(db.DateTime, nullable=False)
    # e.g., 'daily', 'weekly'
    recurrence: Optional[str] = Column(db.String(50), nullable=True)
    event_count: int = Column(db.Integer, default=0, nullable=False)
    status: str = Column(db.String(50), default="pending",
                         nullable=False)  # e.g., 'pending', 'sent'
    # Changed to nullable=True as per typical usage before agent integration
    process_name: Optional[str] = Column(db.Text, nullable=True)
    created_at: dt.datetime = Column(
        db.DateTime, nullable=False, default=db.func.now())
    updated_at: dt.datetime = Column(db.DateTime, nullable=False,
                                     default=db.func.now(), onupdate=db.func.now())

    # Type hints for relationships (backref from Task)
    if TYPE_CHECKING:
        task: Mapped["Task"]

    def __repr__(self):
        """Represent instance as a unique string."""
        return f"<SelfPrompt(Task ID: {self.task_id}, Process: {self.process_name or 'N/A'})>"


# Backward compatibility alias
Reminder = SelfPrompt


# Backward compatibility alias
Reminder = SelfPrompt
