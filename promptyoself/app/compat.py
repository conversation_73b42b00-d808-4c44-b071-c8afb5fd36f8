# -*- coding: utf-8 -*-
"""Python 2/3 compatibility module."""
import sys

PY2 = int(sys.version[0]) == 2

if PY2:  # pragma: no cover
    # Python 2 compatibility (not actually used in Python 3+ environments)
    text_type = unicode  # type: ignore # noqa
    binary_type = str
    string_types = (str, unicode)  # type: ignore # noqa
    unicode = unicode  # type: ignore # noqa
    basestring = basestring  # type: ignore # noqa
else:
    # Python 3+ (actual runtime)
    text_type = str
    binary_type = bytes
    string_types = (str,)
    unicode = str
    basestring = (str, bytes)
