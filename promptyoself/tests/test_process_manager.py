import subprocess

from promptyoself.app.agents.process_manager import ProcessManager
from promptyoself.app.agents.process_registry import ProcessRegistry


def test_start_agent_bad_command(tmp_path):
    manager = ProcessManager(ProcessRegistry())
    info = manager.start_agent("missing", ["/nonexistent/command"])
    assert info is None
    status = manager.process_registry.get_process("missing")
    assert status is None
