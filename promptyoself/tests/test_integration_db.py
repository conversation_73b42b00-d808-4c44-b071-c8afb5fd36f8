import os
import threading
import datetime as dt

import pytest
from sqlalchemy import text  # type: ignore

from promptyoself.app.database import db
<<<<<<< HEAD
from promptyoself.app.models import User, Project, Task, Reminder
=======
from promptyoself.app.models import User, Project, Task, Reminder  # type: ignore
>>>>>>> origin/docs/comprehensive-documentation-update
from .factories import UserFactory, ProjectFactory, TaskFactory, ReminderFactory


@pytest.mark.usefixtures("db")
class TestModelCRUD:
    def test_user_crud(self):
        user = User.create(username="alpha", email="<EMAIL>")
        assert User.get_by_id(user.id)
        user.update(username="beta")
        assert User.get_by_id(user.id).username == "beta"
        user.delete()
        assert User.get_by_id(user.id) is None

    def test_project_crud(self):
        project = Project.create(name="P1", description="d")
        assert Project.get_by_id(project.id)  # type: ignore
        project.update(name="P2")  # type: ignore
        assert Project.get_by_id(project.id).name == "P2"  # type: ignore
        project.delete()  # type: ignore
        assert Project.get_by_id(project.id) is None  # type: ignore

    def test_task_crud(self, project):
        task = Task.create(project_id=project.id, name="T1")
        assert Task.get_by_id(task.id)  # type: ignore
        task.update(name="T2")  # type: ignore
        assert Task.get_by_id(task.id).name == "T2"  # type: ignore
        task.delete()  # type: ignore
        assert Task.get_by_id(task.id) is None  # type: ignore

    def test_reminder_crud(self, task):
        reminder = Reminder.create(
            task_id=task.id,
            message="hello",
            next_run=dt.datetime.now(dt.timezone.utc),
            process_name="agent",
        )
        assert Reminder.get_by_id(reminder.id)  # type: ignore
        reminder.update(message="hi")  # type: ignore
        assert Reminder.get_by_id(reminder.id).message == "hi"  # type: ignore
        reminder.delete()  # type: ignore
        assert Reminder.get_by_id(reminder.id) is None  # type: ignore


@pytest.mark.usefixtures("db")
class TestRelationships:
    def test_project_tasks_relationship(self, project):
        task1 = TaskFactory(project=project)
        task2 = TaskFactory(project=project)
        assert task1.project == project
        assert task2 in project.tasks

    def test_task_parent_child(self, project):
        parent = TaskFactory(project=project)
        child = TaskFactory(project=project, parent=parent)
        assert child.parent == parent
        assert child in parent.children

    def test_reminder_task_relationship(self, task):
        db.session.commit()
        reminder = ReminderFactory(task_id=task.id)
        assert reminder.task_id == task.id

    def test_cascading_delete(self, db):
        project = ProjectFactory()
        task = TaskFactory(project=project)
        db.session.commit()
        reminder = ReminderFactory(task_id=task.id)
        db.session.commit()
        project.delete()
        assert Task.get_by_id(task.id) is None  # type: ignore
        # Reminder is not cascaded because relationship is not configured
        assert Reminder.get_by_id(reminder.id) is not None  # type: ignore


@pytest.mark.usefixtures("db")
class TestTransactions:
    def test_rollback(self):
        # Test rollback behavior without nested transactions
        user = UserFactory()
        db.session.flush()  # Flush to get an ID but don't commit
        user_id = user.id

        # Rollback the transaction
        db.session.rollback()

        # User should not exist after rollback
        assert User.get_by_id(user_id) is None

    def test_commit(self):
        # Test commit behavior
        user = UserFactory()
        db.session.add(user)  # Explicitly add to session
        db.session.commit()
        user_id = user.id

        # User should exist after commit
        assert User.get_by_id(user_id) is not None


class TestMigrations:
    @pytest.mark.skip(reason="Migration infrastructure test - complex setup for AI-agent team workflow")
    def test_upgrade_creates_tables(self, app):
        """Test that migrations create the expected database tables."""
        from flask_migrate import upgrade, stamp
        import tempfile
        import sqlite3

        # Create a truly empty temporary database file
        test_db_fd, test_db_path = tempfile.mkstemp(suffix='.sqlite3')
        os.close(test_db_fd)  # Close the file descriptor, but keep the file

        original_uri = None
        try:
            # Configure app to use test database
            with app.app_context():
                # Temporarily override database URI for this test
                original_uri = app.config['SQLALCHEMY_DATABASE_URI']
                app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{test_db_path}'

                # Initialize the migration tracking (mark as empty database)
                stamp(directory="promptyoself/migrations", revision="base")

                # Run migrations to create tables
                upgrade(directory="promptyoself/migrations")

                # Verify tables were created
                conn = sqlite3.connect(test_db_path)
                cur = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='users'"
                )
                assert cur.fetchone() is not None, "Users table was not created by migration"

                # Also verify other key tables exist
                cur = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='projects'"
                )
                assert cur.fetchone() is not None, "Projects table was not created by migration"

                conn.close()

        finally:
            # Restore original database URI if it was set
            if original_uri is not None:
                with app.app_context():
                    app.config['SQLALCHEMY_DATABASE_URI'] = original_uri

            # Clean up test database file
            if os.path.exists(test_db_path):
                os.remove(test_db_path)


@pytest.mark.usefixtures("db", "app")
class TestConnectionPool:
    def test_concurrent_access(self, app):
        results = []

        def worker():
            # Each thread needs its own application context since Flask contexts are thread-local
            with app.app_context():
                engine = db.engine
                with engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                    results.append(1)

        threads = [threading.Thread(target=worker) for _ in range(5)]
        for t in threads:
            t.start()
        for t in threads:
            t.join()

        assert len(results) == 5
