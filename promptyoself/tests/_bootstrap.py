"""Shared test bootstrap utilities to avoid duplication."""
import sys
import os


def ensure_app_loaded():
    """Ensure the app package is available for import in tests."""
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

    # Try to import the app to verify it's available
    try:
        import promptyoself.app
        return True
    except ImportError:
        return False
