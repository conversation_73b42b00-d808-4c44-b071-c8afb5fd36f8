import datetime as dt
import pytest

from promptyoself.app.models import Project, Task, Reminder
from promptyoself.app.forms import TaskForm
<<<<<<< HEAD
from promptyoself.app.api import tasks as tasks_api
=======
>>>>>>> origin/docs/comprehensive-documentation-update


def test_project_crud(client, db, user):

    res = client.post("/api/projects/",
                      json={"name": "Project A", "description": "desc"})
    assert res.status_code == 201
    project_id = res.get_json()["project"]["id"]

    res = client.get("/api/projects/")
    assert res.status_code == 200
    assert any(p["id"] == project_id for p in res.get_json()["projects"])

    res = client.get(f"/api/projects/{project_id}")
    assert res.status_code == 200

    res = client.put(f"/api/projects/{project_id}",
                     json={"name": "Updated", "description": "d"})
    assert res.status_code == 200
    assert Project.get_by_id(project_id).name == "Updated"

    res = client.delete(f"/api/projects/{project_id}")
    assert res.status_code == 200
    assert Project.get_by_id(project_id) is None
