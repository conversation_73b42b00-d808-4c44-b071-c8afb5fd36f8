import time
import threading
from datetime import datetime, timedelta

import pytest
import requests
from werkzeug.serving import make_server

from promptyoself.app import create_app
from promptyoself.app.database import db
from promptyoself.app.jobs.reminder_jobs import check_due_reminders


class ServerThread(threading.Thread):
    def __init__(self, app, host="127.0.0.1", port=5010):
        super().__init__()
        self.app = app
        self.host = host
        self.port = port
        self.server = None

    def run(self):
        self.server = make_server(self.host, self.port, self.app)
        self.server.serve_forever()

    def shutdown(self):
        if self.server:
            self.server.shutdown()


@pytest.fixture(scope="session")
def live_server():
    app = create_app("tests.settings")
    with app.app_context():
        db.create_all()
    server = ServerThread(app)
    server.start()
    time.sleep(1)
    yield {"url": f"http://{server.host}:{server.port}", "app": app}
    server.shutdown()
    server.join()


def create_project_task(base_url):
    resp = requests.post(
        f"{base_url}/api/projects/",
        json={"name": "E2E Project", "description": "demo"},
        timeout=5,
    )
    resp.raise_for_status()
    project_id = resp.json()["project"]["id"]
    resp = requests.post(
        f"{base_url}/api/tasks/",
        json={
            "name": "E2E Task",
            "description": "demo",
            "project_id": project_id,
            "parent_task_id": None,
        },
        timeout=5,
    )
    resp.raise_for_status()
    task_id = resp.json()["task"]["id"]
    return task_id


@pytest.mark.playwright
def test_user_registration_flow(page, live_server):
    base_url = live_server["url"]
    username = f"user{int(time.time())}"
    page.goto(f"{base_url}/register/")
    page.fill("input[name=username]", username)
    page.fill("input[name=email]", f"{username}@example.com")
    page.fill("input[name=password]", "secret123")
    page.fill("input[name=confirm]", "secret123")
    page.click("text=Register")
    page.wait_for_url(f"{base_url}/")
    assert page.locator("text=User created successfully").is_visible()


@pytest.mark.playwright
def test_reminder_creation_and_delivery(page, live_server):
    base_url = live_server["url"]
    app = live_server["app"]
    task_id = create_project_task(base_url)

    page.goto(f"{base_url}/reminders/new")
    page.select_option("select[name=task_id]", str(task_id))
    page.fill("textarea[name=message]", "E2E reminder")
    # Use longer delay to reduce flakiness
    run_time = (datetime.utcnow() + timedelta(seconds=5)
                ).strftime("%Y-%m-%dT%H:%M")
    page.fill("input[name=next_run]", run_time)
    page.fill("input[name=recurrence]", "")
    page.fill("input[name=process_name]", "test_agent")
    page.click("text=Save Reminder")
    page.wait_for_url(f"{base_url}/reminders/")
    assert page.locator("text=E2E reminder").first.is_visible()

    resp = requests.get(f"{base_url}/api/reminders/", timeout=5)
    resp.raise_for_status()
    reminders = resp.json()["reminders"]
    reminder = [r for r in reminders if r["message"] == "E2E reminder"][0]

    # Poll for reminder status instead of fixed sleep
    max_wait = 10
    start_time = time.time()
    while time.time() - start_time < max_wait:
        with app.app_context():
            check_due_reminders()

        resp = requests.get(
            f"{base_url}/api/reminders/{reminder['id']}", timeout=5)
        resp.raise_for_status()
        if resp.json()["reminder"]["status"] == "sent":
            break
        time.sleep(1)

    # Final assertion
    resp = requests.get(
        f"{base_url}/api/reminders/{reminder['id']}", timeout=5)
    resp.raise_for_status()
    assert resp.json()["reminder"]["status"] == "sent"


@pytest.mark.playwright
def test_registration_validation_error(page, live_server):
    base_url = live_server["url"]
    page.goto(f"{base_url}/register/")
    page.fill("input[name=username]", "bad")
    page.fill("input[name=email]", "<EMAIL>")
    page.fill("input[name=password]", "secret123")
    page.fill("input[name=confirm]", "mismatch")
    page.click("text=Register")

    # Better assertion with error message context
    error_locator = page.locator(".flash-error")
    assert error_locator.is_visible()
    error_text = error_locator.inner_text()
    assert "Passwords must match" in error_text, f"Expected 'Passwords must match' in error message, got: {error_text}"
