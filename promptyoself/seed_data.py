#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database seed script for PromptYoSelf application.

This script populates the database with sample data for development and testing.
It can be run safely multiple times as it checks for existing data before insertion.
"""

from __future__ import annotations  # Ensure this is at the top for forward references

import sys
import logging
import datetime as dt
from typing import List, TYPE_CHECKING, Optional  # Ensure Optional is imported

from faker import Faker

from app import create_app
from app.extensions import db
from app.models import User, Role, Project, Task, Reminder, SelfPrompt

if TYPE_CHECKING:
    pass  # Removed WebhookDelivery forward reference as it's not defined
    # from app.models import WebhookDelivery

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

fake = Faker()


def check_existing_data() -> dict:
    """Check if data already exists in the database."""
    counts = {
        'users': User.query.count(),
        'roles': Role.query.count(),
        'projects': Project.query.count(),
        'tasks': Task.query.count(),
        'reminders': Reminder.query.count(),
        # 'webhook_deliveries': WebhookDelivery.query.count(),
    }
    return counts


def create_sample_users() -> List[User]:
    """Create sample users with different roles."""
    users_data = [
        {
            'username': 'admin_user',
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'Admin',
            'last_name': 'User',
            'active': True,
            'is_admin': True
        },
        {
            'username': 'dev_alice',
            'email': '<EMAIL>',
            'password': 'alice123',
            'first_name': 'Alice',
            'last_name': 'Developer',
            'active': True,
            'is_admin': False
        },
        {
            'username': 'user_bob',
            'email': '<EMAIL>',
            'password': 'bob123',
            'first_name': 'Bob',
            'last_name': 'Smith',
            'active': True,
            'is_admin': False
        }
    ]

    created_users = []
    for user_data in users_data:
        # Check if user already exists
        existing_user = User.query.filter_by(
            username=user_data['username']).first()
        if existing_user:
            logger.info(
                f"User {user_data['username']} already exists, skipping...")
            created_users.append(existing_user)
            continue

        user = User(**user_data)
        db.session.add(user)
        created_users.append(user)
        logger.info(f"Created user: {user_data['username']}")

    return created_users


def create_sample_roles(users: List[User]) -> List[Role]:
    """Create sample roles for users."""
    roles_data = [
        {'name': 'administrator', 'user_id': None},  # Global admin role
        {'name': 'developer', 'user_id': None},     # Global developer role
        {'name': 'user', 'user_id': None},          # Global user role
    ]

    created_roles = []
    for role_data in roles_data:
        # Check if role already exists
        existing_role = Role.query.filter_by(name=role_data['name']).first()
        if existing_role:
            logger.info(
                f"Role {role_data['name']} already exists, skipping...")
            created_roles.append(existing_role)
            continue

        role = Role(**role_data)
        db.session.add(role)
        created_roles.append(role)
        logger.info(f"Created role: {role_data['name']}")

    return created_roles


def create_sample_projects() -> List[Project]:
    """Create sample projects."""
    projects_data = [
        {
            'name': 'PromptYoSelf Development',
            'description': 'Main development project for the PromptYoSelf reminder system'
        },
        {
            'name': 'AI Agent Integration',
            'description': 'Project for integrating various AI agents with the reminder system'
        },
        {
            'name': 'Testing & QA',
            'description': 'Quality assurance and testing project for system reliability'
        }
    ]

    created_projects = []
    for project_data in projects_data:
        # Check if project already exists
        existing_project = Project.query.filter_by(
            name=project_data['name']).first()
        if existing_project:
            logger.info(
                f"Project {project_data['name']} already exists, skipping...")
            created_projects.append(existing_project)
            continue

        project = Project(**project_data)
        db.session.add(project)
        created_projects.append(project)
        logger.info(f"Created project: {project_data['name']}")

    return created_projects


def create_sample_tasks(projects: List[Project]) -> List[Task]:
    """Create sample tasks within projects."""
    if not projects:
        logger.warning("No projects available for task creation")
        return []

    # Tasks for PromptYoSelf Development project
    dev_project = projects[0]
    tasks_data = [
        {
            'project_id': dev_project.id,
            'name': 'Database Schema Design',
            'description': 'Design and implement the database schema for reminders and tasks',
            'parent_task_id': None
        },
        {
            'project_id': dev_project.id,
            'name': 'API Development',
            'description': 'Develop REST API endpoints for reminder management',
            'parent_task_id': None
        },
        {
            'project_id': dev_project.id,
            'name': 'User Authentication',
            'description': 'Implement user authentication and authorization system',
            'parent_task_id': None
        }
    ]

    # Add tasks for AI Agent Integration project if available
    if len(projects) > 1:
        ai_project = projects[1]
        tasks_data.extend([
            {
                'project_id': ai_project.id,
                'name': 'Agent Communication Protocol',
                'description': 'Define communication protocol between reminder system and AI agents',
                'parent_task_id': None
            },
            {
                'project_id': ai_project.id,
                'name': 'Webhook Integration',
                'description': 'Implement webhook system for agent notifications',
                'parent_task_id': None
            }
        ])

    created_tasks = []
    for task_data in tasks_data:
        # Check if task already exists
        existing_task = Task.query.filter_by(
            name=task_data['name'],
            project_id=task_data['project_id']
        ).first()
        if existing_task:
            logger.info(
                f"Task {task_data['name']} already exists, skipping...")
            created_tasks.append(existing_task)
            continue

        task = Task(**task_data)
        db.session.add(task)
        created_tasks.append(task)
        logger.info(f"Created task: {task_data['name']}")

    return created_tasks


def create_sample_reminders(tasks: List[Task]) -> List[Reminder]:  # Use string literal for forward reference
    """Create sample reminders for tasks with various process_name values."""
    if not tasks:
        logger.warning("No tasks available for reminder creation")
        return []

    # Sample process names representing different local agent executables
    process_names = [
        'ai_code_reviewer.py',
        'dependency_updater.exe',
        'security_scanner.sh',
        'performance_monitor.py',
        'backup_manager.exe',
        'log_analyzer.py',
        'test_runner.sh',
        'deployment_checker.py'
    ]

    reminders_data = []
    for i, task in enumerate(tasks[:6]):  # Create reminders for first 6 tasks
        process_name = process_names[i % len(process_names)]

        # Calculate next_run time (various schedules)
        now = dt.datetime.now(dt.timezone.utc)
        if i % 3 == 0:
            next_run = now + dt.timedelta(hours=1)  # 1 hour from now
            recurrence = 'daily'
        elif i % 3 == 1:
            next_run = now + dt.timedelta(days=1)   # 1 day from now
            recurrence = 'weekly'
        else:
            next_run = now + dt.timedelta(minutes=30)  # 30 minutes from now
            recurrence = None  # One-time reminder

        reminder_data = {
            'task_id': task.id,
            'message': f'Reminder for task: {task.name}. Please check progress and update status.',
            'next_run': next_run,
            'recurrence': recurrence,
            'event_count': 0,
            'status': 'pending' if i % 4 != 0 else 'sent',  # Mix of pending and sent
            'process_name': process_name
        }
        reminders_data.append(reminder_data)

    created_reminders = []
    for reminder_data in reminders_data:
        # Check if reminder already exists
        existing_reminder = Reminder.query.filter_by(
            task_id=reminder_data['task_id'],
            process_name=reminder_data['process_name']
        ).first()
        if existing_reminder:
            logger.info(
                f"Reminder for task {reminder_data['task_id']} with process {reminder_data['process_name']} already exists, skipping...")
            created_reminders.append(existing_reminder)
            continue

        reminder = Reminder(**reminder_data)
        db.session.add(reminder)
        created_reminders.append(reminder)
        logger.info(
            f"Created reminder for task {reminder_data['task_id']} with process {reminder_data['process_name']}")

    return created_reminders


# def create_sample_webhook_deliveries(reminders: List[Reminder]) -> List[WebhookDelivery]: # Function remains commented out
#     # ... (rest of the function body commented out)
#     return []


def seed_database():
    """Main function to seed the database with sample data."""
    logger.info("Starting database seeding process...")

    try:
        # Check existing data
        existing_counts = check_existing_data()
        logger.info(f"Existing data counts: {existing_counts}")

        # Create sample data
        logger.info("Creating sample users...")
        users = create_sample_users()

        logger.info("Creating sample roles...")
        roles = create_sample_roles(users)

        logger.info("Creating sample projects...")
        projects = create_sample_projects()

        # Commit users, roles, and projects first to get their IDs
        db.session.commit()
        logger.info("Committed users, roles, and projects to database")

        logger.info("Creating sample tasks...")
        tasks = create_sample_tasks(projects)

        # Commit tasks to get their IDs
        db.session.commit()
        logger.info("Committed tasks to database")

        logger.info("Creating sample reminders...")
        reminders = create_sample_reminders(tasks)

        # Commit reminders to get their IDs
        db.session.commit()
        logger.info("Committed reminders to database")

        # Final counts
        final_counts = check_existing_data()
        logger.info(f"Final data counts: {final_counts}")

        logger.info("Database seeding completed successfully!")

        # Summary
        logger.info("=== SEEDING SUMMARY ===")
        logger.info(f"Users: {len(users)} created/verified")
        logger.info(f"Roles: {len(roles)} created/verified")
        logger.info(f"Projects: {len(projects)} created/verified")
        logger.info(f"Tasks: {len(tasks)} created/verified")
        logger.info(f"Reminders: {len(reminders)} created/verified")
        # logger.info(f"Webhook Deliveries: {len(webhook_deliveries)} created/verified")

    except Exception as e:
        logger.error(f"Error during database seeding: {e}")
        db.session.rollback()
        raise


def verify_seed_data():
    """Verify that the seed data was inserted correctly."""
    logger.info("Verifying seed data...")

    try:
        # Check users
        admin_user = User.query.filter_by(username='admin_user').first()
        if admin_user and admin_user.is_admin:
            logger.info("✓ Admin user created and has admin privileges")
        else:
            logger.error("✗ Admin user not found or lacks admin privileges")

        # Check projects with tasks
        dev_project = Project.query.filter_by(
            name='PromptYoSelf Development').first()
        if dev_project and len(dev_project.tasks) > 0:
            logger.info(
                f"✓ Development project has {len(dev_project.tasks)} tasks")
        else:
            logger.error("✗ Development project not found or has no tasks")

        # Check reminders with different process names
        unique_processes = db.session.query(
            Reminder.process_name).distinct().count()  # Assuming Pylance error, SQLAlchemy handles this
        if unique_processes > 0:
            logger.info(
                f"✓ Found {unique_processes} unique process names in reminders")

            # List some process names
            processes = db.session.query(
                Reminder.process_name).distinct().limit(3).all()  # Assuming Pylance error
            process_list = [p[0] for p in processes]
            logger.info(f"  Sample processes: {', '.join(process_list)}")
        else:
            logger.error("✗ No reminders with process names found")

        # Check webhook deliveries
        # delivery_statuses = db.session.query(WebhookDelivery.status).distinct().all()
        # if delivery_statuses:
        #     status_list = [s[0] for s in delivery_statuses]
        #     logger.info(
        #         f"✓ Webhook deliveries with statuses: {', '.join(status_list)}")
        # else:
        #     logger.error("✗ No webhook deliveries found")

        logger.info("Seed data verification completed!")

    except Exception as e:
        logger.error(f"Error during seed data verification: {e}")
        raise


if __name__ == '__main__':
    # Create Flask app context
    app = create_app()

    with app.app_context():
        try:
            # Seed the database
            seed_database()

            # Verify the seeded data
            verify_seed_data()

            logger.info("Seed script completed successfully!")

        except Exception as e:
            logger.error(f"Seed script failed: {e}")
            sys.exit(1)
